# 题目数据转换工具使用指南

## 概述

本文档介绍如何使用题目数据转换工具，将前端的 `TransformToVoQuestionData` 格式转换为 API 所需的 `QuestionsApi.GeneratedQuestion` 格式。

## 功能特性

- ✅ 将前端题目数据转换为 API 格式
- ✅ 支持单个题目和批量题目转换
- ✅ 提供重新生成题目的请求参数准备
- ✅ 提供保存题目到题库的请求参数准备
- ✅ 完整的类型安全支持
- ✅ 错误处理和数据验证

## 安装和导入

### 基础转换器（Utils 包）

```typescript
import {
  type TransformToVoQuestionData,
  QuestionDataConverter
} from '@sa/utils'
```

### Admin 专用转换器

```typescript
import {
  convertQuestionsToApiGeneratedQuestions,
  convertToApiGeneratedQuestion,
  prepareRegenerateQuestionRequest,
  prepareSaveQuestionsToBankRequest
} from '@/utils/question-converter'
```

## 基本使用

### 1. 单个题目转换

```typescript
// 前端题目数据
const question: TransformToVoQuestionData = {
  id: 'q_12345',
  typeText: '单选题',
  typeId: '2',
  title: '以下哪个是JavaScript的数据类型？',
  componentsName: 'SingleChoice',
  options: [
    { label: 'string', value: 'A' },
    { label: 'number', value: 'B' },
    { label: 'boolean', value: 'C' },
    { label: '以上都是', value: 'D' }
  ],
  correctAnswer: 'D',
  analysis: 'JavaScript有多种基本数据类型，包括string、number、boolean等。',
  knowledgePoints: ['JavaScript基础', '数据类型']
}

// 转换为API格式
const apiQuestion = convertToApiGeneratedQuestion(question)
```

### 2. 批量题目转换

```typescript
const questions: TransformToVoQuestionData[] = [question1, question2, question3]
const apiQuestions = convertQuestionsToApiGeneratedQuestions(questions)
```

## 高级用法

### 1. 重新生成题目

```typescript
function replaceQuestion(question: TransformToVoQuestionData) {
  // 转换题目格式
  const generatedQuestion = convertToApiGeneratedQuestion(question)

  // 准备重新生成请求参数
  const requestParams = prepareRegenerateQuestionRequest(question, {
    aiModelId: 'model_123',
    difficultyLevelName: '中等',
    questionDirectionName: '理解',
    userRequirement: '请生成一道更简单的题目'
  })

  // 调用API
  featchRegenerateQuestion(requestParams)
    .then((newQuestion) => {
      console.log('重新生成的题目:', newQuestion)
    })
}
```

### 2. 保存题目到题库

```typescript
function saveQuestionsToBank(questions: Question.TransformToVoQuestionData[]) {
  // 准备保存请求参数
  const requestParams = prepareSaveQuestionsToBankRequest(questions, {
    chapterId: 'chapter_123',
    difficultyId: 'difficulty_456',
    learningLevelId: 'level_789'
  })

  // 调用API
  saveMultipleQuestionsToBank([requestParams])
    .then((result) => {
      console.log('保存结果:', result)
    })
}
```

## 数据格式说明

### 输入格式 (TransformToVoQuestionData)

```typescript
interface TransformToVoQuestionData {
  id: string // 题目唯一标识符
  typeText: string // 题型文本描述
  typeId: string // 题型ID
  title: string // 题目内容/题干
  componentsName: string // 对应前端组件名称
  options?: Array<{ // 题目选项（选择题）
    label: string // 选项显示文本
    value: string // 选项实际值
  }>
  correctAnswer: string // 正确答案
  analysis: string // 答案解析
  knowledgePoints: string[] // 关联的知识点列表
  userAnswer?: string | string[] // 用户答案（可选）
}
```

### 输出格式 (QuestionsApi.GeneratedQuestion)

```typescript
interface GeneratedQuestion {
  Analysis: string // 答案解析
  Answer: string // 正确答案
  Chapters?: QuestionChapter[] | null // 关联的章节列表
  KnowledgePoints?: QuestionKnowledgePoints[] | null // 关联的知识点列表
  Options?: QuestionOption[] | null // 选项
  QuestionType: string // 题型
  QuestionTypeId: string // 题型ID
  Title: string // 题干
}
```

## 错误处理

转换器包含完整的错误处理机制：

```typescript
try {
  const apiQuestion = convertToApiGeneratedQuestion(question)
}
catch (error) {
  console.error('转换失败:', error.message)
  // 处理错误情况
}
```

常见错误：
- `题目数据不能为空` - 传入的题目数据为 null 或 undefined
- `题目数据必须是数组格式` - 批量转换时传入的不是数组

## 注意事项

1. **知识点ID生成**: 转换器会为知识点生成临时ID（格式：`kp_{questionId}_{index}`），实际使用时可能需要替换为真实的知识点ID。

2. **章节信息**: 前端数据中通常不包含章节信息，转换后的 `Chapters` 字段会被设置为 `undefined`。

3. **类型安全**: 所有转换函数都提供完整的 TypeScript 类型支持，确保类型安全。

4. **数据验证**: 转换器会对输入数据进行基本验证，并提供默认值处理。

## 示例项目

完整的使用示例可以在以下文件中找到：
- `packages/utils/src/question-converter-example.ts` - 基础转换示例
- `apps/admin/src/views/questions/components/right-view/index.vue` - 实际使用场景
