<script setup lang="ts">
defineOptions({
  name: 'EditQuestionDrawer',
})

// 定义props
const props = defineProps<{
  question: Question.TransformToVoQuestionData | null
}>()

// 定义emits
const emit = defineEmits<{
  save: [question: Question.TransformToVoQuestionData]
  cancel: []
}>()

// 抽屉显示状态
const visible = defineModel('visible', {
  type: Boolean,
  default: false,
})

// 监听抽屉关闭
function handleClose() {
  visible.value = false
  emit('cancel')
}

// 保存题目
function handleSave() {
  if (props.question) {
    emit('save', props.question)
    visible.value = false
  }
}
</script>

<template>
  <NDrawer v-model:show="visible" :width="600" placement="right">
    <NDrawerContent title="编辑题目" closable :native-scrollbar="false" @close="handleClose">
      <div class="p-4">
        <!-- 这里是空白内容，后续可以添加编辑表单 -->
        <div class="text-center text-gray-500">
          编辑题目功能开发中...
        </div>
        <div v-if="question" class="mt-4">
          <p class="text-sm text-gray-600">
            当前编辑题目：
          </p>
          <p class="font-medium">
            {{ question.title }}
          </p>
        </div>
      </div>

      <!-- 抽屉底部操作按钮 -->
      <template #footer>
        <div class="flex justify-end gap-3">
          <NButton @click="handleClose">
            取消
          </NButton>
          <NButton type="primary" @click="handleSave">
            保存
          </NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
