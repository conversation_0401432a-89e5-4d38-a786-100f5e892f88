import { QuestionComponentName, QuestionTypeId } from '@sa/enum'

/**
 * 题型ID到组件名称的映射
 */
export const QUESTION_TYPE_COMPONENT_MAP: Record<string, string> = {
  [QuestionTypeId.SINGLE_CHOICE]: QuestionComponentName.SINGLE_CHOICE,
  [QuestionTypeId.TRUE_FALSE]: QuestionComponentName.TRUE_FALSE,
  [QuestionTypeId.MULTIPLE_CHOICE]: QuestionComponentName.MULTIPLE_CHOICE,
  [QuestionTypeId.FILL_BLANK]: QuestionComponentName.FILL_BLANK,
}

/**
 * 获取题型对应的组件名称
 * @param typeId 题型ID
 * @returns 对应的组件名称
 */
export function getQuestionComponentName(typeId: string): string {
  return QUESTION_TYPE_COMPONENT_MAP[typeId] || QuestionComponentName.SINGLE_CHOICE
}
