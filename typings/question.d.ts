declare namespace Question {
  /**
   * 登录模块
   *
   * - single-choice: 单选题
   * - MultipleChoice: 多选题
   * - TrueFalse: 判断题
   * - FillBlank: 填空题
   */
    type QuestionModule = 'SingleChoice' | 'MultipleChoice' | 'TrueFalse' | 'FillBlank'
    // edit 编辑 answer 回答 preview 预览
    type QuestionType = 'edit' | 'answer' | 'preview'
    // 定义流式数据的接口
    interface QuestionData {
      Question: {
        QuestionType: string
        QuestionTypeId: string
        Title: string
        Options: QuestionOption[] | null // 只有选择，判断，多选题有选项
        Answer: string
        Analysis: string
        Chapters: QuestionChapter[] | null // 只有章节出题时才会有数据
        KnowledgePoints: QuestionKnowledgePoints[] | null // 只有知识点出题时才会有数据
      }
    }
    interface QuestionOption {
    /**
     * 选项内容
     */
      Content: string
      /**
       * 选项标识（A、B、C、D）
       */
      Option: string
    }
    interface QuestionChapter {
    /**
     * 章节名称
     */
      ChapterName: string
      /**
       * 章节ID
       */
      Id: string
    }
    interface QuestionKnowledgePoints {
    /**
     * 知识点内容
     */
      Content: string
      /**
       * 知识点ID
       */
      Id: string
      /**
       * 知识点层级
       */
      Level: number
    }

}
